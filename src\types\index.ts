// Global type definitions for the portfolio application

// Navigation types
export interface NavLink {
  href: string;
  label: string;
}

// Project types
export interface Project {
  id: string;
  title: string;
  description: string;
  longDescription: string;
  image: string;
  technologies: string[];
  category: ProjectCategory;
  liveUrl: string;
  repoUrl: string;
  featured: boolean;
  completedDate: string;
  status: ProjectStatus;
}

export type ProjectCategory = 'AI/ML' | 'E-commerce' | 'Web Development' | 'Mobile' | 'Desktop';
export type ProjectStatus = 'completed' | 'in-progress' | 'planned';

// Contact form types
export interface ContactFormData {
  name: string;
  email: string;
  subject: string;
  message: string;
}

export interface ContactFormErrors {
  name?: string;
  email?: string;
  subject?: string;
  message?: string;
}

export type ContactFormStatus = 'idle' | 'loading' | 'success' | 'error';

export interface ContactInfo {
  icon: React.ReactNode;
  label: string;
  value: string;
  href: string | null;
}

// Skills and experience types
export interface Skill {
  name: string;
  level: number;
  category: SkillCategory;
}

export type SkillCategory = 'Frontend' | 'Backend' | 'Database' | 'DevOps' | 'AI' | 'Mobile';

export interface Experience {
  title: string;
  company: string;
  period: string;
  description: string;
  technologies: string[];
}

export interface Achievement {
  title: string;
  description: string;
  date: string;
  icon: React.ReactNode;
}

// Theme types
export type Theme = 'light' | 'dark' | 'system';

// Animation types
export interface AnimationVariants {
  initial: Record<string, unknown>;
  animate: Record<string, unknown>;
  exit?: Record<string, unknown>;
  transition?: Record<string, unknown>;
}

// API response types
export interface ApiResponse<T = unknown> {
  success: boolean;
  data?: T;
  error?: string;
  message?: string;
}

export interface ApiError {
  message: string;
  code?: string | number;
  details?: unknown;
}

// Form validation types
export interface ValidationRule {
  required?: boolean;
  minLength?: number;
  maxLength?: number;
  pattern?: RegExp;
  custom?: (value: string) => boolean;
}

export interface ValidationResult {
  isValid: boolean;
  errors: string[];
}

// Social media types
export interface SocialLink {
  platform: SocialPlatform;
  url: string;
  icon: React.ReactNode;
  label: string;
}

export type SocialPlatform = 'github' | 'linkedin' | 'twitter' | 'email' | 'website';

// SEO types
export interface SEOData {
  title: string;
  description: string;
  keywords?: string[];
  ogImage?: string;
  canonicalUrl?: string;
}

// Component prop types
export interface BaseComponentProps {
  className?: string;
  children?: React.ReactNode;
}

export interface MotionComponentProps extends BaseComponentProps {
  initial?: Record<string, unknown>;
  animate?: Record<string, unknown>;
  exit?: Record<string, unknown>;
  transition?: Record<string, unknown>;
  delay?: number;
}

// Utility types
export type Optional<T, K extends keyof T> = Omit<T, K> & Partial<Pick<T, K>>;
export type RequiredFields<T, K extends keyof T> = T & Required<Pick<T, K>>;

// Event handler types
export type FormSubmitHandler = (event: React.FormEvent<HTMLFormElement>) => void | Promise<void>;
export type InputChangeHandler = (event: React.ChangeEvent<HTMLInputElement>) => void;
export type TextareaChangeHandler = (event: React.ChangeEvent<HTMLTextAreaElement>) => void;
export type ButtonClickHandler = (event: React.MouseEvent<HTMLButtonElement>) => void;

// Loading and error states
export interface LoadingState {
  isLoading: boolean;
  error: string | null;
}

export interface AsyncState<T = unknown> extends LoadingState {
  data: T | null;
}

// Configuration types
export interface AppConfig {
  siteName: string;
  siteUrl: string;
  author: {
    name: string;
    email: string;
    social: SocialLink[];
  };
  features: {
    darkMode: boolean;
    animations: boolean;
    analytics: boolean;
  };
}

// Filter and search types
export interface FilterOptions {
  category?: ProjectCategory;
  status?: ProjectStatus;
  featured?: boolean;
  technologies?: string[];
}

export interface SearchOptions {
  query: string;
  filters?: FilterOptions;
  sortBy?: 'date' | 'name' | 'category';
  sortOrder?: 'asc' | 'desc';
}

// Error boundary types
export interface ErrorInfo {
  componentStack: string;
  errorBoundary?: string;
}

export interface ErrorBoundaryState {
  hasError: boolean;
  error: Error | null;
  errorInfo: ErrorInfo | null;
}

// Performance monitoring types
export interface PerformanceMetrics {
  loadTime: number;
  renderTime: number;
  interactionTime: number;
}

// Accessibility types
export interface A11yProps {
  'aria-label'?: string;
  'aria-labelledby'?: string;
  'aria-describedby'?: string;
  role?: string;
  tabIndex?: number;
}

// Type guards
export const isProject = (obj: unknown): obj is Project => {
  return typeof obj === 'object' && obj !== null && 'id' in obj && 'title' in obj;
};

export const isContactFormData = (obj: unknown): obj is ContactFormData => {
  return typeof obj === 'object' && obj !== null && 
         'name' in obj && 'email' in obj && 'subject' in obj && 'message' in obj;
};

export const isApiResponse = <T = unknown>(obj: unknown): obj is ApiResponse<T> => {
  return typeof obj === 'object' && obj !== null && 'success' in obj;
};

// Utility functions for type safety
export const assertNever = (value: never): never => {
  throw new Error(`Unexpected value: ${value}`);
};

export const isNonNullable = <T>(value: T): value is NonNullable<T> => {
  return value !== null && value !== undefined;
};
