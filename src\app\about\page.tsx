'use client';

import { motion } from "framer-motion";
import { Card, CardContent } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Progress } from "@/components/ui/progress";
import {
  Code,
  MapPin,
  Mail,
  Download
} from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";
import type { Skill } from "@/types";



const skills: Skill[] = [
  { name: "JavaScript/TypeScript", level: 95, category: "Frontend" },
  { name: "React/Next.js", level: 90, category: "Frontend" },
  { name: "Node.js/Express", level: 85, category: "Backend" },
  { name: "MongoDB/PostgreSQL", level: 80, category: "Database" },
  { name: "Python", level: 75, category: "Backend" },
  { name: "AWS/Cloud", level: 70, category: "DevOps" },
  { name: "Docker", level: 65, category: "DevOps" },
  { name: "AI/ML Integration", level: 80, category: "AI" },
];



export default function AboutPage() {
  return (
    <div className="min-h-screen bg-gradient-to-b from-background to-muted/20">
      <div className="max-w-6xl mx-auto px-4 py-12">
        {/* Hero Section */}
        <motion.div
          className="text-center mb-16"
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
        >
          <h1 className="text-4xl sm:text-5xl font-bold mb-6 bg-gradient-to-r from-primary to-primary/70 bg-clip-text text-transparent">
            About Me
          </h1>
          <p className="text-muted-foreground text-lg max-w-3xl mx-auto leading-relaxed">
            I&apos;m a passionate Full Stack Developer with expertise in modern web technologies,
            AI integrations, and creating scalable applications that solve real-world problems.
          </p>
        </motion.div>

        {/* Personal Info Card */}
        <motion.div
          className="mb-12"
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.2, duration: 0.6 }}
        >
          <Card className="bg-card/50 backdrop-blur-sm border-0">
            <CardContent className="p-8">
              <div className="grid md:grid-cols-2 gap-8 items-center">
                <div>
                  <h2 className="text-2xl font-bold mb-4">Hello, I&apos;m Xmart</h2>
                  <p className="text-muted-foreground mb-6 leading-relaxed">
                    A dedicated Full Stack Developer with a passion for creating innovative web solutions.
                    I specialize in the MERN stack, Next.js, and cutting-edge AI integrations.
                    My goal is to build applications that not only look great but also provide
                    exceptional user experiences and solve complex business challenges.
                  </p>
                  <div className="flex flex-wrap gap-4 mb-6">
                    <div className="flex items-center text-sm text-muted-foreground">
                      <MapPin className="w-4 h-4 mr-2" />
                      Remote / Global
                    </div>
                    <div className="flex items-center text-sm text-muted-foreground">
                      <Mail className="w-4 h-4 mr-2" />
                      Available for projects
                    </div>
                  </div>
                  <Button className="mr-4">
                    <Download className="w-4 h-4 mr-2" />
                    Download CV
                  </Button>
                  <Button variant="outline">
                    <Mail className="w-4 h-4 mr-2" />
                    Get in Touch
                  </Button>
                </div>
                <div className="relative">
                  <div className="w-64 h-64 mx-auto bg-gradient-to-br from-primary/20 to-primary/5 rounded-full flex items-center justify-center">
                    <Code className="w-24 h-24 text-primary" />
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </motion.div>

        {/* Skills Section */}
        <motion.div
          className="mb-12"
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.4, duration: 0.6 }}
        >
          <h2 className="text-3xl font-bold mb-8 text-center">Technical Skills</h2>
          <div className="grid md:grid-cols-2 gap-6">
            {skills.map((skill, index) => (
              <motion.div
                key={skill.name}
                initial={{ opacity: 0, x: -20 }}
                animate={{ opacity: 1, x: 0 }}
                transition={{ delay: 0.1 * index, duration: 0.5 }}
              >
                <Card className="bg-card/50 backdrop-blur-sm border-0">
                  <CardContent className="p-6">
                    <div className="flex justify-between items-center mb-3">
                      <div>
                        <h3 className="font-semibold">{skill.name}</h3>
                        <Badge variant="secondary" className="text-xs mt-1">
                          {skill.category}
                        </Badge>
                      </div>
                      <span className="text-sm font-medium text-primary">
                        {skill.level}%
                      </span>
                    </div>
                    <Progress value={skill.level} className="h-2" />
                  </CardContent>
                </Card>
              </motion.div>
            ))}
          </div>
        </motion.div>
      </div>
    </div>
  );
}
