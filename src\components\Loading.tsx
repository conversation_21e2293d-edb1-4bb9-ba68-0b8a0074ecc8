'use client';

import { motion } from 'framer-motion';
import { Loader2 } from 'lucide-react';
import { Card, CardContent } from '@/components/ui/card';
import type { BaseComponentProps } from '@/types';

interface LoadingSpinnerProps extends BaseComponentProps {
  size?: 'sm' | 'md' | 'lg';
  text?: string;
}

export function LoadingSpinner({ size = 'md', text, className }: LoadingSpinnerProps) {
  const sizeClasses = {
    sm: 'w-4 h-4',
    md: 'w-6 h-6',
    lg: 'w-8 h-8',
  };

  return (
    <div className={`flex items-center justify-center gap-2 ${className}`}>
      <Loader2 className={`animate-spin ${sizeClasses[size]}`} />
      {text && <span className="text-muted-foreground">{text}</span>}
    </div>
  );
}

interface LoadingSkeletonProps extends BaseComponentProps {
  width?: string;
  height?: string;
  rounded?: boolean;
}

export function LoadingSkeleton({ 
  width = 'w-full', 
  height = 'h-4', 
  rounded = true,
  className 
}: LoadingSkeletonProps) {
  return (
    <div 
      className={`
        bg-muted animate-pulse 
        ${width} ${height} 
        ${rounded ? 'rounded' : ''} 
        ${className}
      `}
    />
  );
}

interface LoadingCardProps extends BaseComponentProps {
  showImage?: boolean;
  lines?: number;
}

export function LoadingCard({ showImage = true, lines = 3, className }: LoadingCardProps) {
  return (
    <Card className={`bg-card/50 backdrop-blur-sm border-0 ${className}`}>
      <CardContent className="p-6">
        {showImage && (
          <LoadingSkeleton height="h-48" className="mb-4" />
        )}
        <LoadingSkeleton height="h-6" width="w-3/4" className="mb-3" />
        {Array.from({ length: lines }).map((_, i) => (
          <LoadingSkeleton 
            key={i}
            height="h-4" 
            width={i === lines - 1 ? 'w-1/2' : 'w-full'}
            className="mb-2"
          />
        ))}
        <div className="flex gap-2 mt-4">
          <LoadingSkeleton height="h-8" width="w-20" />
          <LoadingSkeleton height="h-8" width="w-16" />
        </div>
      </CardContent>
    </Card>
  );
}

interface LoadingGridProps extends BaseComponentProps {
  count?: number;
  columns?: number;
  showImage?: boolean;
}

export function LoadingGrid({ 
  count = 6, 
  columns = 3, 
  showImage = true,
  className 
}: LoadingGridProps) {
  const gridClasses = {
    1: 'grid-cols-1',
    2: 'grid-cols-1 md:grid-cols-2',
    3: 'grid-cols-1 md:grid-cols-2 lg:grid-cols-3',
    4: 'grid-cols-1 md:grid-cols-2 lg:grid-cols-4',
  };

  return (
    <div className={`grid gap-6 ${gridClasses[columns as keyof typeof gridClasses]} ${className}`}>
      {Array.from({ length: count }).map((_, i) => (
        <motion.div
          key={i}
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: i * 0.1, duration: 0.5 }}
        >
          <LoadingCard showImage={showImage} />
        </motion.div>
      ))}
    </div>
  );
}

interface LoadingPageProps extends BaseComponentProps {
  title?: string;
  subtitle?: string;
}

export function LoadingPage({ title = 'Loading...', subtitle, className }: LoadingPageProps) {
  return (
    <div className={`min-h-screen flex items-center justify-center p-4 ${className}`}>
      <div className="text-center">
        <motion.div
          initial={{ scale: 0.8, opacity: 0 }}
          animate={{ scale: 1, opacity: 1 }}
          transition={{ duration: 0.5 }}
        >
          <LoadingSpinner size="lg" />
        </motion.div>
        <motion.h2 
          className="text-2xl font-bold mt-4"
          initial={{ opacity: 0, y: 10 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.2, duration: 0.5 }}
        >
          {title}
        </motion.h2>
        {subtitle && (
          <motion.p 
            className="text-muted-foreground mt-2"
            initial={{ opacity: 0, y: 10 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.4, duration: 0.5 }}
          >
            {subtitle}
          </motion.p>
        )}
      </div>
    </div>
  );
}

interface LoadingButtonProps extends BaseComponentProps {
  isLoading: boolean;
  children: React.ReactNode;
  loadingText?: string;
}

export function LoadingButton({ 
  isLoading, 
  children, 
  loadingText = 'Loading...',
  className 
}: LoadingButtonProps) {
  return (
    <span className={`inline-flex items-center ${className}`}>
      {isLoading ? (
        <>
          <Loader2 className="w-4 h-4 mr-2 animate-spin" />
          {loadingText}
        </>
      ) : (
        children
      )}
    </span>
  );
}

// Pulse animation for loading states
export function PulseLoader({ className }: BaseComponentProps) {
  return (
    <div className={`flex space-x-1 ${className}`}>
      {[0, 1, 2].map((i) => (
        <motion.div
          key={i}
          className="w-2 h-2 bg-primary rounded-full"
          animate={{
            scale: [1, 1.2, 1],
            opacity: [0.7, 1, 0.7],
          }}
          transition={{
            duration: 1,
            repeat: Infinity,
            delay: i * 0.2,
          }}
        />
      ))}
    </div>
  );
}

// Progress bar for loading states
interface LoadingProgressProps extends BaseComponentProps {
  progress: number;
  showPercentage?: boolean;
}

export function LoadingProgress({ 
  progress, 
  showPercentage = true,
  className 
}: LoadingProgressProps) {
  return (
    <div className={`w-full ${className}`}>
      <div className="flex justify-between items-center mb-2">
        <span className="text-sm text-muted-foreground">Loading...</span>
        {showPercentage && (
          <span className="text-sm text-muted-foreground">{Math.round(progress)}%</span>
        )}
      </div>
      <div className="w-full bg-muted rounded-full h-2">
        <motion.div
          className="bg-primary h-2 rounded-full"
          initial={{ width: 0 }}
          animate={{ width: `${progress}%` }}
          transition={{ duration: 0.3 }}
        />
      </div>
    </div>
  );
}

// Shimmer effect for loading content
export function ShimmerEffect({ className }: BaseComponentProps) {
  return (
    <div className={`relative overflow-hidden bg-muted ${className}`}>
      <motion.div
        className="absolute inset-0 bg-gradient-to-r from-transparent via-white/20 to-transparent"
        animate={{
          x: ['-100%', '100%'],
        }}
        transition={{
          duration: 1.5,
          repeat: Infinity,
          ease: 'linear',
        }}
      />
    </div>
  );
}
