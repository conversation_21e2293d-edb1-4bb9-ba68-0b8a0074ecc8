@import "tailwindcss";
@import "tw-animate-css";

/* @custom-variant dark (&:is(.dark *)); */



:root {
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --font-sans: var(--font-geist-sans);
  --font-mono: var(--font-geist-mono);
  --color-sidebar-ring: var(--sidebar-ring);
  --color-sidebar-border: var(--sidebar-border);
  --color-sidebar-accent-foreground: var(--sidebar-accent-foreground);
  --color-sidebar-accent: var(--sidebar-accent);
  --color-sidebar-primary-foreground: var(--sidebar-primary-foreground);
  --color-sidebar-primary: var(--sidebar-primary);
  --color-sidebar-foreground: var(--sidebar-foreground);
  --color-sidebar: var(--sidebar);
  --color-chart-5: var(--chart-5);
  --color-chart-4: var(--chart-4);
  --color-chart-3: var(--chart-3);
  --color-chart-2: var(--chart-2);
  --color-chart-1: var(--chart-1);
  --color-ring: var(--ring);
  --color-input: var(--input);
  --color-border: var(--border);
  --color-destructive: var(--destructive);
  --color-accent-foreground: var(--accent-foreground);
  --color-accent: var(--accent);
  --color-muted-foreground: var(--muted-foreground);
  --color-muted: var(--muted);
  --color-secondary-foreground: var(--secondary-foreground);
  --color-secondary: var(--secondary);
  --color-primary-foreground: var(--primary-foreground);
  --color-primary: var(--primary);
  --color-popover-foreground: var(--popover-foreground);
  --color-popover: var(--popover);
  --color-card-foreground: var(--card-foreground);
  --color-card: var(--card);
  --radius-sm: calc(var(--radius) - 4px);
  --radius-md: calc(var(--radius) - 2px);
  --radius-lg: var(--radius);
  --radius-xl: calc(var(--radius) + 4px);
}

:root {
  --radius: 0.75rem;

  /* Modern Color Palette - Light Mode */
  --background: oklch(0.99 0.005 106);
  --foreground: oklch(0.15 0.02 258);
  --card: oklch(1 0 0);
  --card-foreground: oklch(0.15 0.02 258);
  --popover: oklch(1 0 0);
  --popover-foreground: oklch(0.15 0.02 258);

  /* Primary - Modern Blue */
  --primary: oklch(0.45 0.15 258);
  --primary-foreground: oklch(0.98 0.005 258);

  /* Secondary - Warm Gray */
  --secondary: oklch(0.96 0.01 258);
  --secondary-foreground: oklch(0.25 0.02 258);

  /* Muted - Subtle Gray */
  --muted: oklch(0.95 0.01 258);
  --muted-foreground: oklch(0.55 0.015 258);

  /* Accent - Electric Cyan */
  --accent: oklch(0.7 0.15 195);
  --accent-foreground: oklch(0.98 0.005 195);

  /* Success - Modern Green */
  --success: oklch(0.6 0.15 142);
  --success-foreground: oklch(0.98 0.005 142);

  /* Warning - Vibrant Orange */
  --warning: oklch(0.7 0.15 65);
  --warning-foreground: oklch(0.15 0.02 65);

  /* Destructive - Modern Red */
  --destructive: oklch(0.6 0.2 25);
  --destructive-foreground: oklch(0.98 0.005 25);

  /* Borders and Inputs */
  --border: oklch(0.9 0.01 258);
  --input: oklch(0.92 0.01 258);
  --ring: oklch(0.45 0.15 258);

  /* Chart Colors */
  --chart-1: oklch(0.6 0.15 258);
  --chart-2: oklch(0.65 0.15 195);
  --chart-3: oklch(0.6 0.15 142);
  --chart-4: oklch(0.7 0.15 65);
  --chart-5: oklch(0.6 0.2 25);

  /* Sidebar */
  --sidebar: oklch(0.98 0.005 258);
  --sidebar-foreground: oklch(0.15 0.02 258);
  --sidebar-primary: oklch(0.45 0.15 258);
  --sidebar-primary-foreground: oklch(0.98 0.005 258);
  --sidebar-accent: oklch(0.95 0.01 258);
  --sidebar-accent-foreground: oklch(0.25 0.02 258);
  --sidebar-border: oklch(0.9 0.01 258);
  --sidebar-ring: oklch(0.45 0.15 258);

  /* Gradient Variables */
  --gradient-primary: linear-gradient(135deg, oklch(0.45 0.15 258), oklch(0.7 0.15 195));
  --gradient-secondary: linear-gradient(135deg, oklch(0.7 0.15 195), oklch(0.6 0.15 142));
  --gradient-accent: linear-gradient(135deg, oklch(0.6 0.15 142), oklch(0.7 0.15 65));
}

.dark {
  /* Modern Color Palette - Dark Mode */
  --background: oklch(0.08 0.01 258);
  --foreground: oklch(0.95 0.005 258);
  --card: oklch(0.12 0.015 258);
  --card-foreground: oklch(0.95 0.005 258);
  --popover: oklch(0.12 0.015 258);
  --popover-foreground: oklch(0.95 0.005 258);

  /* Primary - Bright Blue */
  --primary: oklch(0.65 0.2 258);
  --primary-foreground: oklch(0.08 0.01 258);

  /* Secondary - Dark Gray */
  --secondary: oklch(0.18 0.015 258);
  --secondary-foreground: oklch(0.9 0.005 258);

  /* Muted - Medium Gray */
  --muted: oklch(0.15 0.015 258);
  --muted-foreground: oklch(0.65 0.01 258);

  /* Accent - Bright Cyan */
  --accent: oklch(0.75 0.2 195);
  --accent-foreground: oklch(0.08 0.01 195);

  /* Success - Bright Green */
  --success: oklch(0.7 0.2 142);
  --success-foreground: oklch(0.08 0.01 142);

  /* Warning - Bright Orange */
  --warning: oklch(0.75 0.2 65);
  --warning-foreground: oklch(0.08 0.01 65);

  /* Destructive - Bright Red */
  --destructive: oklch(0.7 0.25 25);
  --destructive-foreground: oklch(0.95 0.005 25);

  /* Borders and Inputs */
  --border: oklch(0.25 0.02 258);
  --input: oklch(0.2 0.015 258);
  --ring: oklch(0.65 0.2 258);

  /* Chart Colors - Dark Mode */
  --chart-1: oklch(0.65 0.2 258);
  --chart-2: oklch(0.75 0.2 195);
  --chart-3: oklch(0.7 0.2 142);
  --chart-4: oklch(0.75 0.2 65);
  --chart-5: oklch(0.7 0.25 25);

  /* Sidebar - Dark Mode */
  --sidebar: oklch(0.1 0.01 258);
  --sidebar-foreground: oklch(0.95 0.005 258);
  --sidebar-primary: oklch(0.65 0.2 258);
  --sidebar-primary-foreground: oklch(0.08 0.01 258);
  --sidebar-accent: oklch(0.15 0.015 258);
  --sidebar-accent-foreground: oklch(0.9 0.005 258);
  --sidebar-border: oklch(0.25 0.02 258);
  --sidebar-ring: oklch(0.65 0.2 258);

  /* Gradient Variables - Dark Mode */
  --gradient-primary: linear-gradient(135deg, oklch(0.65 0.2 258), oklch(0.75 0.2 195));
  --gradient-secondary: linear-gradient(135deg, oklch(0.75 0.2 195), oklch(0.7 0.2 142));
  --gradient-accent: linear-gradient(135deg, oklch(0.7 0.2 142), oklch(0.75 0.2 65));
}

@layer base {
  * {
    border-color: hsl(var(--border));
    outline-color: hsl(var(--ring) / 0.5);
  }

  body {
    background: hsl(var(--background));
    color: hsl(var(--foreground));
    font-family: var(--font-sans), system-ui, sans-serif;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    font-feature-settings: "rlig" 1, "calt" 1;
  }

  /* Modern Typography Scale */
  h1 {
    font-size: clamp(2.25rem, 5vw, 4rem);
    font-weight: 700;
    letter-spacing: -0.025em;
    line-height: 1.1;
  }

  h2 {
    font-size: clamp(1.875rem, 4vw, 3rem);
    font-weight: 700;
    letter-spacing: -0.025em;
    line-height: 1.2;
  }

  h3 {
    font-size: clamp(1.5rem, 3vw, 2rem);
    font-weight: 600;
    letter-spacing: -0.025em;
    line-height: 1.3;
  }

  h4 {
    font-size: clamp(1.25rem, 2.5vw, 1.5rem);
    font-weight: 600;
    letter-spacing: -0.025em;
    line-height: 1.4;
  }

  h5 {
    font-size: clamp(1.125rem, 2vw, 1.25rem);
    font-weight: 500;
    line-height: 1.5;
  }

  h6 {
    font-size: clamp(1rem, 1.5vw, 1.125rem);
    font-weight: 500;
    line-height: 1.5;
  }

  p {
    font-size: 1rem;
    line-height: 1.625;
  }

  /* Focus styles */
  :focus-visible {
    outline: 2px solid hsl(var(--ring));
    outline-offset: 2px;
  }
}

@layer components {
  /* Modern Gradient Backgrounds */
  .gradient-primary {
    background: var(--gradient-primary);
  }

  .gradient-secondary {
    background: var(--gradient-secondary);
  }

  .gradient-accent {
    background: var(--gradient-accent);
  }

  /* Glass morphism effect */
  .glass {
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(12px);
    border: 1px solid rgba(255, 255, 255, 0.2);
  }

  .glass-dark {
    background: rgba(0, 0, 0, 0.1);
    backdrop-filter: blur(12px);
    border: 1px solid rgba(255, 255, 255, 0.1);
  }



  /* Animated gradients */
  .animated-gradient {
    background: linear-gradient(-45deg,
      oklch(0.45 0.15 258),
      oklch(0.7 0.15 195),
      oklch(0.6 0.15 142),
      oklch(0.7 0.15 65)
    );
    background-size: 400% 400%;
    animation: gradient 15s ease infinite;
  }

  @keyframes gradient {
    0% { background-position: 0% 50%; }
    50% { background-position: 100% 50%; }
    100% { background-position: 0% 50%; }
  }

  /* Modern button styles */
  .btn-modern {
    position: relative;
    overflow: hidden;
    border-radius: 0.75rem;
    padding: 0.75rem 1.5rem;
    font-weight: 600;
    transition: all 0.3s;
    background: hsl(var(--primary));
    color: hsl(var(--primary-foreground));
  }

  .btn-modern:hover {
    transform: scale(1.05);
    box-shadow: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
  }

  .btn-modern::before {
    content: '';
    position: absolute;
    inset: 0;
    background: linear-gradient(to right, transparent, rgba(255, 255, 255, 0.2), transparent);
    transform: translateX(-100%);
    transition: transform 0.6s;
  }

  .btn-modern:hover::before {
    transform: translateX(100%);
  }

  /* Text effects */
  .text-gradient {
    background: linear-gradient(to right, hsl(var(--primary)), hsl(var(--accent)), hsl(var(--primary)));
    background-clip: text;
    -webkit-background-clip: text;
    color: transparent;
    background-size: 200% auto;
    animation: shimmer 3s linear infinite;
  }

  @keyframes shimmer {
    to { background-position: 200% center; }
  }

  /* Modern card styles */
  .card-modern {
    position: relative;
    border-radius: 1rem;
    border: 1px solid hsl(var(--border) / 0.5);
    background: hsl(var(--card) / 0.5);
    backdrop-filter: blur(12px);
    box-shadow: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
    transition: all 0.3s;
  }

  .card-modern:hover {
    box-shadow: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
  }

  .card-modern::before {
    content: '';
    position: absolute;
    inset: 0;
    border-radius: 1rem;
    background: linear-gradient(to bottom right, rgba(255, 255, 255, 0.05), transparent);
    pointer-events: none;
  }

  /* Scroll animations */
  .fade-in-up {
    opacity: 0;
    transform: translateY(30px);
    transition: opacity 0.6s ease, transform 0.6s ease;
  }

  .fade-in-up.animate {
    opacity: 1;
    transform: translateY(0);
  }

  /* Loading states */
  .skeleton {
    animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
    background: hsl(var(--muted));
    border-radius: 0.375rem;
  }

  .pulse-dot {
    width: 0.5rem;
    height: 0.5rem;
    background: hsl(var(--primary));
    border-radius: 50%;
    animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
  }
}

