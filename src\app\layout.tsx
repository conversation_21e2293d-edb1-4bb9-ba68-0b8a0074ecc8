// app/layout.tsx
import './globals.css';
import { Inter } from 'next/font/google';
import { cn } from '@/lib/utils';
import { ThemeProvider } from '@/components/ThemeProvider';
import { Navbar } from '@/components/NavBar';
import { ErrorBoundary } from '@/components/ErrorBoundary';
import { SkipToMain } from '@/components/A11y';
import type { Metadata } from 'next';

const inter = Inter({ subsets: ['latin'] });

export const metadata: Metadata = {
  title: 'Sameer Dev | Full Stack Developer',
  description: 'I build scalable web apps with MERN, Next.js, and AI integrations.',
  openGraph: {
    title: 'Sameer Dev Portfolio',
    description: 'Full stack portfolio with projects, contact, and about info.',
    url: 'https://sameer-dev.online', // Update this
    siteName: 'sameer-dev.online',
    images: [
      {
        url: '/og-image.png', // Add your OG image in public folder
        width: 1200,
        height: 630,
        alt: 'Sameer Dev Portfolio Website',
      },
    ],
    locale: 'en_US',
    type: 'website',
  },
};

export default function RootLayout({ children }: { children: React.ReactNode }) {
  return (
    <html lang="en" suppressHydrationWarning>
      <body className={cn(inter.className, 'bg-background text-foreground')}>
        <SkipToMain />
        <ThemeProvider attribute="class" defaultTheme="system" enableSystem>
          <ErrorBoundary>
            <Navbar />
            <main id="main-content" className="pt-16">
              {children}
            </main>
          </ErrorBoundary>
        </ThemeProvider>
      </body>
    </html>
  );
}
